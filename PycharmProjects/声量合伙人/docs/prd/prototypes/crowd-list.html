<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>人群列表页 - 声量合伙人</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --el-color-primary: #409eff;
            --el-color-success: #67c23a;
            --el-color-warning: #e6a23c;
            --el-color-danger: #f56c6c;
            --el-color-info: #909399;
            --el-border-color: #dcdfe6;
            --el-border-color-light: #e4e7ed;
            --el-text-color-primary: #303133;
            --el-text-color-regular: #606266;
            --el-text-color-secondary: #909399;
            --el-bg-color: #ffffff;
            --el-bg-color-page: #f2f3f5;
        }
        
        body {
            font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", Arial, sans-serif;
            background-color: var(--el-bg-color-page);
        }
        
        .el-card {
            background: var(--el-bg-color);
            border: 1px solid var(--el-border-color-light);
            border-radius: 4px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
        }
        
        .el-button {
            padding: 8px 15px;
            border-radius: 4px;
            border: 1px solid var(--el-border-color);
            background: var(--el-bg-color);
            color: var(--el-text-color-regular);
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .el-button--primary {
            background: var(--el-color-primary);
            border-color: var(--el-color-primary);
            color: white;
        }
        
        .el-button--success {
            background: var(--el-color-success);
            border-color: var(--el-color-success);
            color: white;
        }
        
        .el-button--warning {
            background: var(--el-color-warning);
            border-color: var(--el-color-warning);
            color: white;
        }
        
        .el-button--danger {
            background: var(--el-color-danger);
            border-color: var(--el-color-danger);
            color: white;
        }
        
        .el-input {
            width: 100%;
            position: relative;
            font-size: 14px;
            display: inline-block;
        }
        
        .el-input__inner {
            width: 100%;
            height: 32px;
            line-height: 32px;
            padding: 0 15px;
            border: 1px solid var(--el-border-color);
            border-radius: 4px;
            color: var(--el-text-color-regular);
            background: var(--el-bg-color);
            transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
        }
        
        .el-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .el-table th {
            background: #f8f8f9;
            color: #515a6e;
            font-weight: 500;
            padding: 12px 0;
            border-bottom: 1px solid var(--el-border-color-light);
        }
        
        .el-table td {
            padding: 12px 0;
            border-bottom: 1px solid var(--el-border-color-light);
        }
        
        .el-tag {
            display: inline-block;
            height: 24px;
            padding: 0 8px;
            line-height: 22px;
            font-size: 12px;
            border-radius: 4px;
            border: 1px solid;
        }
        
        .el-tag--success {
            background: #f0f9ff;
            border-color: #b3d8ff;
            color: #409eff;
        }
        
        .el-tag--warning {
            background: #fdf6ec;
            border-color: #f5dab1;
            color: #e6a23c;
        }
        
        .el-tag--danger {
            background: #fef0f0;
            border-color: #fbc4c4;
            color: #f56c6c;
        }
        
        .annotation {
            position: absolute;
            background: #ff4444;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 1000;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }
        
        .annotation::after {
            content: '';
            position: absolute;
            top: 100%;
            left: 50%;
            margin-left: -5px;
            border: 5px solid transparent;
            border-top-color: #ff4444;
        }
        
        .highlight-new {
            background: rgba(255, 68, 68, 0.1);
            border: 2px dashed #ff4444;
            border-radius: 4px;
            position: relative;
        }
    </style>
</head>
<body>
    <!-- 顶部导航 -->
    <div class="bg-white shadow-sm border-b border-gray-200 mb-4">
        <div class="px-6 py-3">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <a href="./index.html" class="text-blue-500 hover:text-blue-700 mr-4">
                        <i class="fas fa-arrow-left"></i> 返回导航
                    </a>
                    <h1 class="text-lg font-semibold text-gray-900">人群管理</h1>
                </div>
                <div class="text-sm text-gray-500">管理后台 > 人群管理 > 人群列表</div>
            </div>
        </div>
    </div>

    <div class="p-6">
        <!-- 搜索区域 -->
        <div class="el-card mb-4">
            <div class="p-5">
                <div class="flex flex-wrap items-end gap-4">
                    <div class="flex-1 min-w-48">
                        <label class="block text-sm font-medium text-gray-700 mb-1">人群名称</label>
                        <div class="el-input">
                            <input type="text" class="el-input__inner" placeholder="请输入人群名称" value="">
                        </div>
                    </div>
                    <div class="w-32">
                        <label class="block text-sm font-medium text-gray-700 mb-1">人群状态</label>
                        <select class="el-input__inner">
                            <option value="">全部状态</option>
                            <option value="1">正常</option>
                            <option value="2">停用</option>
                        </select>
                    </div>
                    <div class="w-32">
                        <label class="block text-sm font-medium text-gray-700 mb-1">人群类型</label>
                        <select class="el-input__inner">
                            <option value="">全部类型</option>
                            <option value="1">Excel导入</option>
                            <option value="2">条件筛选</option>
                        </select>
                    </div>
                    <div class="flex gap-2">
                        <button class="el-button el-button--primary">
                            <i class="fas fa-search mr-1"></i>查询
                        </button>
                        <button class="el-button">
                            <i class="fas fa-refresh mr-1"></i>重置
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 操作区域和表格 -->
        <div class="el-card highlight-new">
            <!-- 新增功能标注 -->
            <div class="annotation" style="top: -35px; left: 20px;">
                新增：人群管理功能
            </div>
            
            <div class="p-5 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="flex gap-2">
                        <button class="el-button el-button--primary">
                            <i class="fas fa-plus mr-1"></i>新建人群
                        </button>
                        <button class="el-button el-button--success">
                            <i class="fas fa-download mr-1"></i>导出
                        </button>
                        <button class="el-button el-button--warning">
                            <i class="fas fa-upload mr-1"></i>批量导入
                        </button>
                    </div>
                    <div class="flex items-center gap-2">
                        <button class="el-button" title="刷新">
                            <i class="fas fa-refresh"></i>
                        </button>
                        <button class="el-button" title="列设置">
                            <i class="fas fa-cog"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 表格内容 -->
            <div class="overflow-x-auto">
                <table class="el-table">
                    <thead>
                        <tr>
                            <th class="text-center w-12">
                                <input type="checkbox" class="rounded">
                            </th>
                            <th class="text-center w-20">ID</th>
                            <th class="text-left">人群名称</th>
                            <th class="text-center">人群编码</th>
                            <th class="text-center">人群类型</th>
                            <th class="text-center">总用户数</th>
                            <th class="text-center">有效用户数</th>
                            <th class="text-center">匹配率</th>
                            <th class="text-center">状态</th>
                            <th class="text-center">创建时间</th>
                            <th class="text-center w-48">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="text-center">
                                <input type="checkbox" class="rounded">
                            </td>
                            <td class="text-center">1001</td>
                            <td class="text-left">
                                <div class="font-medium">VIP客户群体</div>
                                <div class="text-xs text-gray-500">高价值客户精准营销</div>
                            </td>
                            <td class="text-center">VIP_CUSTOMERS_001</td>
                            <td class="text-center">
                                <span class="el-tag el-tag--success">Excel导入</span>
                            </td>
                            <td class="text-center">1,250</td>
                            <td class="text-center text-green-600 font-medium">1,180</td>
                            <td class="text-center">
                                <span class="text-green-600 font-medium">94.4%</span>
                            </td>
                            <td class="text-center">
                                <span class="el-tag el-tag--success">正常</span>
                            </td>
                            <td class="text-center text-gray-500">2025-01-28 14:30</td>
                            <td class="text-center">
                                <div class="flex justify-center gap-1">
                                    <button class="el-button text-blue-600 hover:bg-blue-50 px-2 py-1 text-sm">查看</button>
                                    <button class="el-button text-green-600 hover:bg-green-50 px-2 py-1 text-sm">编辑</button>
                                    <button class="el-button text-orange-600 hover:bg-orange-50 px-2 py-1 text-sm">统计</button>
                                    <button class="el-button text-red-600 hover:bg-red-50 px-2 py-1 text-sm">删除</button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="text-center">
                                <input type="checkbox" class="rounded">
                            </td>
                            <td class="text-center">1002</td>
                            <td class="text-left">
                                <div class="font-medium">新用户推广群</div>
                                <div class="text-xs text-gray-500">新注册用户引导转化</div>
                            </td>
                            <td class="text-center">NEW_USERS_002</td>
                            <td class="text-center">
                                <span class="el-tag el-tag--success">Excel导入</span>
                            </td>
                            <td class="text-center">2,800</td>
                            <td class="text-center text-green-600 font-medium">2,650</td>
                            <td class="text-center">
                                <span class="text-green-600 font-medium">94.6%</span>
                            </td>
                            <td class="text-center">
                                <span class="el-tag el-tag--success">正常</span>
                            </td>
                            <td class="text-center text-gray-500">2025-01-27 09:15</td>
                            <td class="text-center">
                                <div class="flex justify-center gap-1">
                                    <button class="el-button text-blue-600 hover:bg-blue-50 px-2 py-1 text-sm">查看</button>
                                    <button class="el-button text-green-600 hover:bg-green-50 px-2 py-1 text-sm">编辑</button>
                                    <button class="el-button text-orange-600 hover:bg-orange-50 px-2 py-1 text-sm">统计</button>
                                    <button class="el-button text-red-600 hover:bg-red-50 px-2 py-1 text-sm">删除</button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="text-center">
                                <input type="checkbox" class="rounded">
                            </td>
                            <td class="text-center">1003</td>
                            <td class="text-left">
                                <div class="font-medium">销售部门推广</div>
                                <div class="text-xs text-gray-500">销售团队内部推广</div>
                            </td>
                            <td class="text-center">SALES_TEAM_003</td>
                            <td class="text-center">
                                <span class="el-tag el-tag--success">Excel导入</span>
                            </td>
                            <td class="text-center">450</td>
                            <td class="text-center text-orange-600 font-medium">380</td>
                            <td class="text-center">
                                <span class="text-orange-600 font-medium">84.4%</span>
                            </td>
                            <td class="text-center">
                                <span class="el-tag el-tag--warning">停用</span>
                            </td>
                            <td class="text-center text-gray-500">2025-01-25 16:45</td>
                            <td class="text-center">
                                <div class="flex justify-center gap-1">
                                    <button class="el-button text-blue-600 hover:bg-blue-50 px-2 py-1 text-sm">查看</button>
                                    <button class="el-button text-green-600 hover:bg-green-50 px-2 py-1 text-sm">编辑</button>
                                    <button class="el-button text-orange-600 hover:bg-orange-50 px-2 py-1 text-sm">统计</button>
                                    <button class="el-button text-red-600 hover:bg-red-50 px-2 py-1 text-sm">删除</button>
                                </div>
                            </td>
                        </tr>
                        <tr class="bg-yellow-50">
                            <td class="text-center">
                                <input type="checkbox" class="rounded">
                            </td>
                            <td class="text-center">1004</td>
                            <td class="text-left">
                                <div class="font-medium">测试人群数据</div>
                                <div class="text-xs text-gray-500">导入处理中...</div>
                            </td>
                            <td class="text-center">TEST_CROWD_004</td>
                            <td class="text-center">
                                <span class="el-tag el-tag--success">Excel导入</span>
                            </td>
                            <td class="text-center">1,000</td>
                            <td class="text-center text-gray-500">处理中</td>
                            <td class="text-center">
                                <div class="flex items-center justify-center">
                                    <div class="w-16 bg-gray-200 rounded-full h-2 mr-2">
                                        <div class="bg-blue-500 h-2 rounded-full" style="width: 65%"></div>
                                    </div>
                                    <span class="text-sm text-gray-600">65%</span>
                                </div>
                            </td>
                            <td class="text-center">
                                <span class="el-tag el-tag--warning">处理中</span>
                            </td>
                            <td class="text-center text-gray-500">2025-01-30 10:20</td>
                            <td class="text-center">
                                <div class="flex justify-center gap-1">
                                    <button class="el-button text-blue-600 hover:bg-blue-50 px-2 py-1 text-sm">查看进度</button>
                                    <button class="el-button text-red-600 hover:bg-red-50 px-2 py-1 text-sm">取消</button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            <div class="flex items-center justify-between p-4 border-t border-gray-200">
                <div class="text-sm text-gray-500">
                    共 4 条记录，第 1/1 页
                </div>
                <div class="flex items-center gap-2">
                    <button class="el-button" disabled>上一页</button>
                    <button class="el-button el-button--primary">1</button>
                    <button class="el-button" disabled>下一页</button>
                </div>
            </div>
        </div>

        <!-- 功能说明 -->
        <div class="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h3 class="font-medium text-blue-800 mb-2">
                <i class="fas fa-info-circle mr-2"></i>功能说明
            </h3>
            <div class="text-sm text-blue-700 space-y-1">
                <p><strong>1. 人群列表：</strong>显示所有创建的人群，包括基础信息、用户统计、匹配率等</p>
                <p><strong>2. 搜索筛选：</strong>支持按人群名称、状态、类型进行筛选查询</p>
                <p><strong>3. 批量操作：</strong>支持批量导入、导出、删除等操作</p>
                <p><strong>4. 实时状态：</strong>显示导入进度，处理中的人群会实时更新状态</p>
                <p><strong>5. 匹配率：</strong>显示导入用户与系统用户的匹配成功率</p>
            </div>
        </div>

        <!-- 开发备注 -->
        <div class="mt-4 bg-orange-50 border border-orange-200 rounded-lg p-4">
            <h3 class="font-medium text-orange-800 mb-2">
                <i class="fas fa-code mr-2"></i>开发备注
            </h3>
            <div class="text-sm text-orange-700 space-y-1">
                <p><strong>API接口：</strong>GET /admin/crowd/list - 获取人群列表</p>
                <p><strong>权限控制：</strong>需要 crowd:list 权限</p>
                <p><strong>分页参数：</strong>pageNum, pageSize, crowdName, status, type</p>
                <p><strong>实时更新：</strong>导入进度需要通过WebSocket或轮询更新</p>
                <p><strong>表格排序：</strong>支持按创建时间、用户数量等字段排序</p>
            </div>
        </div>
    </div>
</body>
</html>
