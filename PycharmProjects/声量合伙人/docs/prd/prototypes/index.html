<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>声量合伙人 - 人群功能原型导航</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --el-color-primary: #409eff;
            --el-color-primary-light-3: #79bbff;
            --el-color-primary-light-5: #a0cfff;
            --el-color-primary-light-7: #c6e2ff;
            --el-color-primary-light-8: #d9ecff;
            --el-color-primary-light-9: #ecf5ff;
            --el-border-color: #dcdfe6;
            --el-border-color-light: #e4e7ed;
            --el-text-color-primary: #303133;
            --el-text-color-regular: #606266;
            --el-text-color-secondary: #909399;
            --el-text-color-placeholder: #a8abb2;
            --el-bg-color: #ffffff;
            --el-bg-color-page: #f2f3f5;
        }
        
        body {
            font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
            background-color: var(--el-bg-color-page);
        }
        
        .prototype-card {
            background: var(--el-bg-color);
            border: 1px solid var(--el-border-color-light);
            border-radius: 4px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
            transition: all 0.3s;
        }
        
        .prototype-card:hover {
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        }
        
        .nav-item {
            color: var(--el-text-color-regular);
            text-decoration: none;
            padding: 12px 16px;
            border-radius: 4px;
            transition: all 0.3s;
            display: block;
        }
        
        .nav-item:hover {
            background-color: var(--el-color-primary-light-9);
            color: var(--el-color-primary);
        }
        
        .status-badge {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-complete {
            background-color: #f0f9ff;
            color: #1890ff;
        }
        
        .status-progress {
            background-color: #fff7e6;
            color: #fa8c16;
        }
        
        .status-pending {
            background-color: #f6f6f6;
            color: #666;
        }
    </style>
</head>
<body class="min-h-screen">
    <!-- 顶部导航 -->
    <header class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <i class="fas fa-users text-blue-500 text-2xl mr-3"></i>
                    <h1 class="text-xl font-semibold text-gray-900">声量合伙人 - 人群功能原型</h1>
                </div>
                <div class="text-sm text-gray-500">
                    版本：v1.0 | 更新时间：2025-01-30
                </div>
            </div>
        </div>
    </header>

    <!-- 主要内容 -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 项目概述 -->
        <div class="prototype-card p-6 mb-8">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">
                <i class="fas fa-info-circle text-blue-500 mr-2"></i>
                项目概述
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="text-center">
                    <div class="text-2xl font-bold text-blue-600">8</div>
                    <div class="text-sm text-gray-600">原型页面</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-green-600">4</div>
                    <div class="text-sm text-gray-600">核心功能</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-purple-600">15+</div>
                    <div class="text-sm text-gray-600">交互组件</div>
                </div>
            </div>
            <div class="mt-4 p-4 bg-blue-50 rounded-lg">
                <p class="text-sm text-blue-800">
                    <i class="fas fa-lightbulb mr-2"></i>
                    <strong>功能说明：</strong>人群功能是在现有任务发布系统基础上，增加基于Excel导入用户清单的精准推送能力，支持跨部门的营销活动管理。
                </p>
            </div>
        </div>

        <!-- 原型页面导航 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- 管理后台页面 -->
            <div class="prototype-card">
                <div class="p-6 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                        <i class="fas fa-desktop text-blue-500 mr-2"></i>
                        管理后台页面
                    </h3>
                    <p class="text-sm text-gray-600 mt-1">基于Element Plus的管理界面</p>
                </div>
                <div class="p-6">
                    <div class="space-y-3">
                        <a href="./crowd-list.html" class="nav-item flex items-center justify-between">
                            <div class="flex items-center">
                                <i class="fas fa-list text-gray-400 mr-3"></i>
                                <div>
                                    <div class="font-medium">人群列表页</div>
                                    <div class="text-xs text-gray-500">人群管理的主入口页面</div>
                                </div>
                            </div>
                            <span class="status-badge status-complete">已完成</span>
                        </a>
                        
                        <a href="./crowd-create.html" class="nav-item flex items-center justify-between">
                            <div class="flex items-center">
                                <i class="fas fa-plus text-gray-400 mr-3"></i>
                                <div>
                                    <div class="font-medium">人群创建页</div>
                                    <div class="text-xs text-gray-500">创建人群和Excel导入</div>
                                </div>
                            </div>
                            <span class="status-badge status-complete">已完成</span>
                        </a>
                        
                        <a href="./crowd-detail.html" class="nav-item flex items-center justify-between">
                            <div class="flex items-center">
                                <i class="fas fa-eye text-gray-400 mr-3"></i>
                                <div>
                                    <div class="font-medium">人群详情页</div>
                                    <div class="text-xs text-gray-500">人群信息和用户列表</div>
                                </div>
                            </div>
                            <span class="status-badge status-complete">已完成</span>
                        </a>
                        
                        <a href="./task-create.html" class="nav-item flex items-center justify-between">
                            <div class="flex items-center">
                                <i class="fas fa-tasks text-gray-400 mr-3"></i>
                                <div>
                                    <div class="font-medium">任务发布页（改造）</div>
                                    <div class="text-xs text-gray-500">支持人群推送的任务发布</div>
                                </div>
                            </div>
                            <span class="status-badge status-complete">已完成</span>
                        </a>
                        
                        <a href="./task-statistics.html" class="nav-item flex items-center justify-between">
                            <div class="flex items-center">
                                <i class="fas fa-chart-bar text-gray-400 mr-3"></i>
                                <div>
                                    <div class="font-medium">任务统计页（改造）</div>
                                    <div class="text-xs text-gray-500">多维度统计分析</div>
                                </div>
                            </div>
                            <span class="status-badge status-complete">已完成</span>
                        </a>
                    </div>
                </div>
            </div>

            <!-- H5移动端页面 -->
            <div class="prototype-card">
                <div class="p-6 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                        <i class="fas fa-mobile-alt text-green-500 mr-2"></i>
                        H5移动端页面
                    </h3>
                    <p class="text-sm text-gray-600 mt-1">基于Vant4的移动端界面</p>
                </div>
                <div class="p-6">
                    <div class="space-y-3">
                        <a href="./h5-task-list.html" class="nav-item flex items-center justify-between">
                            <div class="flex items-center">
                                <i class="fas fa-list text-gray-400 mr-3"></i>
                                <div>
                                    <div class="font-medium">任务列表页（改造）</div>
                                    <div class="text-xs text-gray-500">显示任务来源标识</div>
                                </div>
                            </div>
                            <span class="status-badge status-complete">已完成</span>
                        </a>
                        
                        <a href="./h5-task-detail.html" class="nav-item flex items-center justify-between">
                            <div class="flex items-center">
                                <i class="fas fa-eye text-gray-400 mr-3"></i>
                                <div>
                                    <div class="font-medium">任务详情页（改造）</div>
                                    <div class="text-xs text-gray-500">显示推送信息</div>
                                </div>
                            </div>
                            <span class="status-badge status-complete">已完成</span>
                        </a>
                        
                        <a href="./h5-profile.html" class="nav-item flex items-center justify-between">
                            <div class="flex items-center">
                                <i class="fas fa-user text-gray-400 mr-3"></i>
                                <div>
                                    <div class="font-medium">个人中心页（改造）</div>
                                    <div class="text-xs text-gray-500">显示人群标签信息</div>
                                </div>
                            </div>
                            <span class="status-badge status-complete">已完成</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- 设计规范说明 -->
        <div class="prototype-card p-6 mt-8">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">
                <i class="fas fa-palette text-purple-500 mr-2"></i>
                设计规范说明
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h4 class="font-medium text-gray-900 mb-2">管理后台设计规范</h4>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• 基于Element Plus组件库</li>
                        <li>• 主色调：#409eff（蓝色）</li>
                        <li>• 卡片式布局，圆角4px</li>
                        <li>• 表格行高适中，支持排序筛选</li>
                        <li>• 表单标签宽度150px，字体加粗</li>
                        <li>• 按钮间距10px，图标+文字组合</li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-medium text-gray-900 mb-2">H5移动端设计规范</h4>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• 基于Vant4组件库</li>
                        <li>• 主色调：#3366cc（深蓝色）</li>
                        <li>• 圆角设计：6px（默认）、12px（大）</li>
                        <li>• 按钮高度：48px（大）、36px（默认）</li>
                        <li>• 间距使用4的倍数（4px、8px、16px）</li>
                        <li>• 文字层级：主要、次要、说明、占位</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 开发备注 -->
        <div class="prototype-card p-6 mt-8">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">
                <i class="fas fa-code text-orange-500 mr-2"></i>
                开发备注
            </h3>
            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <h4 class="font-medium text-yellow-800 mb-2">重要提醒</h4>
                <ul class="text-sm text-yellow-700 space-y-1">
                    <li>1. 所有原型页面都包含详细的功能说明和交互备注</li>
                    <li>2. 红色标注的区域为新增功能，需要重点关注</li>
                    <li>3. 蓝色标注的区域为改造功能，需要保持兼容性</li>
                    <li>4. 绿色标注的区域为数据展示，需要注意数据格式</li>
                    <li>5. 原型中的数据均为示例数据，实际开发时需要对接真实API</li>
                </ul>
            </div>
        </div>
    </main>

    <!-- 底部信息 -->
    <footer class="bg-white border-t border-gray-200 mt-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div class="text-center text-sm text-gray-500">
                <p>声量合伙人 - 人群功能产品原型 | 设计版本：v1.0 | 创建时间：2025-01-30</p>
                <p class="mt-1">基于现有系统设计风格，高度还原真实页面效果</p>
            </div>
        </div>
    </footer>
</body>
</html>
