<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>人群列表页 - 声量合伙人</title>
    <link href="https://unpkg.com/element-plus@2.4.4/dist/index.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        /* 完全基于项目实际样式的CSS变量 */
        :root {
            --menuBg: #304156;
            --menuColor: #bfcbd9;
            --menuActiveText: #f4f4f5;
            --menuHover: #263445;
            --subMenuBg: #1f2d3d;
            --subMenuActiveText: #f4f4f5;
            --subMenuHover: #001528;
            --subMenuTitleHover: #293444;
            --fixedHeaderBg: #ffffff;
            --tableHeaderBg: #f8f8f9;
            --tableHeaderTextColor: #515a6e;
            --brder-color: #e8e8e8;
            --tags-view-active-bg: var(--el-color-primary);
            --tags-view-active-border-color: var(--el-color-primary);
        }

        /* 基础样式 - 完全按照项目index.scss */
        body {
            height: 100%;
            margin: 0;
            -moz-osx-font-smoothing: grayscale;
            -webkit-font-smoothing: antialiased;
            text-rendering: optimizeLegibility;
            font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
        }

        html {
            height: 100%;
            box-sizing: border-box;
        }

        *,
        *:before,
        *:after {
            box-sizing: inherit;
        }

        label {
            font-weight: 700;
        }

        /* RuoYi 通用样式类 - 完全按照ruoyi.scss */
        .pt5 { padding-top: 5px; }
        .pr5 { padding-right: 5px; }
        .pb5 { padding-bottom: 5px; }
        .mt5 { margin-top: 5px; }
        .mr5 { margin-right: 5px; }
        .mb5 { margin-bottom: 5px; }
        .mb8 { margin-bottom: 8px; }
        .ml5 { margin-left: 5px; }
        .mt10 { margin-top: 10px; }
        .mr10 { margin-right: 10px; }
        .mb10 { margin-bottom: 10px; }
        .ml10 { margin-left: 10px; }
        .mt20 { margin-top: 20px; }
        .mr20 { margin-right: 20px; }
        .mb20 { margin-bottom: 20px; }
        .ml20 { margin-left: 20px; }

        /* 主容器样式 - 按照index.scss */
        .app-container {
            padding: 20px;
        }

        /* 搜索面板样式 - 按照index.scss */
        .panel,
        .search {
            margin-bottom: 0.75rem;
            border-radius: 0.25rem;
            border: 1px solid var(--el-border-color-light);
            background-color: var(--el-bg-color-overlay);
            padding: 0.75rem;
            transition: all ease 0.3s;
        }

        .panel:hover,
        .search:hover {
            box-shadow: 0 2px 12px #0000001a;
            transition: all ease 0.3s;
        }

        /* Element Plus 表格样式覆盖 - 按照ruoyi.scss */
        .el-table .el-table__header-wrapper th,
        .el-table .el-table__fixed-header-wrapper th {
            word-break: break-word;
            background-color: var(--tableHeaderBg) !important;
            color: var(--tableHeaderTextColor);
            height: 40px !important;
            font-size: 13px;
        }

        .el-table .el-table__body-wrapper .el-button [class*='el-icon-'] + span {
            margin-left: 1px;
        }

        /* Element Plus 卡片样式覆盖 - 按照ruoyi.scss */
        .el-card__header {
            padding: 14px 15px 14px !important;
            min-height: 40px;
        }

        .el-card__body {
            padding: 20px 20px 20px 20px !important;
        }

        /* Element Plus 表单样式覆盖 - 按照ruoyi.scss */
        .el-form .el-form-item__label {
            font-weight: 700;
        }

        /* 分页容器样式 - 按照ruoyi.scss */
        .pagination-container {
            height: 25px;
            margin-bottom: 10px;
            margin-top: 15px;
            padding: 10px 20px !important;
        }

        /* 表格右侧工具栏样式 - 按照ruoyi.scss */
        .top-right-btn {
            margin-left: auto;
        }

        /* 原型标注样式 */
        .annotation {
            position: absolute;
            background: #ff4444;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 1000;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }
        
        .annotation::after {
            content: '';
            position: absolute;
            top: 100%;
            left: 50%;
            margin-left: -5px;
            border: 5px solid transparent;
            border-top-color: #ff4444;
        }
        
        .highlight-new {
            background: rgba(255, 68, 68, 0.05);
            border: 2px dashed #ff4444;
            border-radius: 4px;
            position: relative;
        }

        /* 面包屑样式 */
        .breadcrumb {
            margin-bottom: 10px;
            color: var(--el-text-color-secondary);
            font-size: 12px;
        }

        .breadcrumb a {
            color: var(--el-color-primary);
            text-decoration: none;
        }

        .breadcrumb a:hover {
            color: var(--el-color-primary-light-3);
        }
    </style>
</head>
<body>
    <!-- RuoYi 标准页面容器 -->
    <div class="app-container">
        <!-- 面包屑导航 -->
        <div class="breadcrumb">
            <a href="./index.html">
                <i class="fas fa-arrow-left" style="margin-right: 5px;"></i>返回导航
            </a>
            <span style="margin: 0 8px;">/</span>
            人群管理
            <span style="margin: 0 8px;">/</span>
            人群列表
        </div>

        <!-- 搜索表单 -->
        <el-card class="search mb10">
            <el-form :inline="true" :model="queryParams" class="demo-form-inline">
                <el-form-item label="人群名称" label-width="80px">
                    <el-input v-model="queryParams.crowdName" placeholder="请输入人群名称" style="width: 200px;" />
                </el-form-item>
                <el-form-item label="人群状态" label-width="80px">
                    <el-select v-model="queryParams.status" placeholder="全部状态" style="width: 120px;">
                        <el-option label="全部状态" value="" />
                        <el-option label="正常" value="1" />
                        <el-option label="停用" value="2" />
                    </el-select>
                </el-form-item>
                <el-form-item label="人群类型" label-width="80px">
                    <el-select v-model="queryParams.type" placeholder="全部类型" style="width: 120px;">
                        <el-option label="全部类型" value="" />
                        <el-option label="Excel导入" value="1" />
                        <el-option label="条件筛选" value="2" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="handleQuery" class="mr10">
                        <i class="fas fa-search" style="margin-right: 5px;"></i>查询
                    </el-button>
                    <el-button @click="resetQuery">
                        <i class="fas fa-refresh" style="margin-right: 5px;"></i>重置
                    </el-button>
                </el-form-item>
            </el-form>
        </el-card>

        <!-- 表格区域 -->
        <el-card shadow="hover" class="highlight-new">
            <!-- 新增功能标注 -->
            <div class="annotation" style="top: -35px; left: 20px;">
                新增：人群管理功能
            </div>
            
            <template #header>
                <el-row :gutter="10">
                    <el-col :span="1.5">
                        <el-button type="primary" plain icon="Plus" @click="handleAdd" class="mr10">
                            新建人群
                        </el-button>
                    </el-col>
                    <el-col :span="1.5">
                        <el-button type="success" plain icon="Download" @click="handleExport" class="mr10">
                            导出
                        </el-button>
                    </el-col>
                    <el-col :span="1.5">
                        <el-button type="warning" plain icon="Upload" @click="handleImport" class="mr10">
                            批量导入
                        </el-button>
                    </el-col>
                    <div class="top-right-btn">
                        <el-tooltip content="刷新" placement="top">
                            <el-button circle icon="Refresh" @click="handleQuery" />
                        </el-tooltip>
                        <el-tooltip content="列设置" placement="top">
                            <el-button circle icon="Setting" @click="handleColumnSetting" class="ml10" />
                        </el-tooltip>
                    </div>
                </el-row>
            </template>

            <el-table v-loading="loading" :data="crowdList" style="width: 100%" show-overflow-tooltip>
                <el-table-column type="selection" width="50" align="center" />
                <el-table-column prop="id" label="ID" align="center" width="80" />
                <el-table-column prop="crowdName" label="人群名称" align="left" min-width="200">
                    <template #default="{ row }">
                        <div>
                            <div style="font-weight: 500;">{{ row.crowdName }}</div>
                            <div style="font-size: 12px; color: var(--el-text-color-secondary); margin-top: 2px;">
                                {{ row.crowdDesc }}
                            </div>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column prop="crowdCode" label="人群编码" align="center" width="150" />
                <el-table-column prop="crowdType" label="人群类型" align="center" width="100">
                    <template #default="{ row }">
                        <el-tag v-if="row.crowdType === 1" type="success">Excel导入</el-tag>
                        <el-tag v-else-if="row.crowdType === 2" type="info">条件筛选</el-tag>
                        <el-tag v-else type="warning">API导入</el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="totalCount" label="总用户数" align="center" width="100" />
                <el-table-column prop="validCount" label="有效用户数" align="center" width="100">
                    <template #default="{ row }">
                        <span style="color: var(--el-color-success); font-weight: 500;">{{ row.validCount }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="匹配率" align="center" width="80">
                    <template #default="{ row }">
                        <span style="color: var(--el-color-success); font-weight: 500;">
                            {{ ((row.validCount / row.totalCount) * 100).toFixed(1) }}%
                        </span>
                    </template>
                </el-table-column>
                <el-table-column prop="crowdStatus" label="状态" align="center" width="80">
                    <template #default="{ row }">
                        <el-tag v-if="row.crowdStatus === 1" type="success">正常</el-tag>
                        <el-tag v-else-if="row.crowdStatus === 2" type="warning">停用</el-tag>
                        <el-tag v-else-if="row.crowdStatus === 0" type="info">处理中</el-tag>
                        <el-tag v-else type="danger">删除</el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="createTime" label="创建时间" align="center" width="150" />
                <el-table-column label="操作" align="center" width="200" fixed="right">
                    <template #default="{ row }">
                        <el-button link type="primary" size="small" @click="handleView(row)">查看</el-button>
                        <el-button link type="primary" size="small" @click="handleEdit(row)">编辑</el-button>
                        <el-button link type="primary" size="small" @click="handleStats(row)">统计</el-button>
                        <el-button link type="danger" size="small" @click="handleDelete(row)">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>

            <div class="pagination-container">
                <el-pagination
                    v-show="total > 0"
                    v-model:current-page="queryParams.pageNum"
                    v-model:page-size="queryParams.pageSize"
                    :page-sizes="[10, 20, 50, 100]"
                    :small="false"
                    :disabled="false"
                    :background="true"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="total"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                />
            </div>
        </el-card>
    </div>

    <!-- Vue 3 和 Element Plus -->
    <script src="https://unpkg.com/vue@3.3.8/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus@2.4.4/dist/index.full.js"></script>
    <script>
        const { createApp, ref, reactive, onMounted } = Vue;
        
        createApp({
            setup() {
                const loading = ref(false);
                const total = ref(0);
                
                const queryParams = reactive({
                    pageNum: 1,
                    pageSize: 10,
                    crowdName: '',
                    status: '',
                    type: ''
                });
                
                const crowdList = ref([
                    {
                        id: 1001,
                        crowdName: 'VIP客户群体',
                        crowdDesc: '高价值客户精准营销',
                        crowdCode: 'VIP_CUSTOMERS_001',
                        crowdType: 1,
                        totalCount: 1250,
                        validCount: 1180,
                        crowdStatus: 1,
                        createTime: '2025-01-28 14:30'
                    },
                    {
                        id: 1002,
                        crowdName: '新用户推广群',
                        crowdDesc: '新注册用户引导转化',
                        crowdCode: 'NEW_USERS_002',
                        crowdType: 1,
                        totalCount: 2800,
                        validCount: 2650,
                        crowdStatus: 1,
                        createTime: '2025-01-27 09:15'
                    },
                    {
                        id: 1003,
                        crowdName: '销售部门推广',
                        crowdDesc: '销售团队内部推广',
                        crowdCode: 'SALES_TEAM_003',
                        crowdType: 1,
                        totalCount: 450,
                        validCount: 380,
                        crowdStatus: 2,
                        createTime: '2025-01-25 16:45'
                    },
                    {
                        id: 1004,
                        crowdName: '测试人群数据',
                        crowdDesc: '导入处理中...',
                        crowdCode: 'TEST_CROWD_004',
                        crowdType: 1,
                        totalCount: 1000,
                        validCount: 0,
                        crowdStatus: 0,
                        createTime: '2025-01-30 10:20'
                    }
                ]);
                
                total.value = crowdList.value.length;
                
                const handleQuery = () => {
                    console.log('查询', queryParams);
                };
                
                const resetQuery = () => {
                    queryParams.crowdName = '';
                    queryParams.status = '';
                    queryParams.type = '';
                    handleQuery();
                };
                
                const handleAdd = () => {
                    window.open('./crowd-create.html', '_blank');
                };
                
                const handleView = (row) => {
                    window.open('./crowd-detail.html', '_blank');
                };
                
                const handleEdit = (row) => {
                    console.log('编辑', row);
                };
                
                const handleStats = (row) => {
                    console.log('统计', row);
                };
                
                const handleDelete = (row) => {
                    ElementPlus.ElMessageBox.confirm('确定删除该人群吗？', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        ElementPlus.ElMessage.success('删除成功');
                    });
                };
                
                const handleExport = () => {
                    console.log('导出');
                };
                
                const handleImport = () => {
                    console.log('批量导入');
                };
                
                const handleColumnSetting = () => {
                    console.log('列设置');
                };
                
                const handleSizeChange = (val) => {
                    queryParams.pageSize = val;
                    handleQuery();
                };
                
                const handleCurrentChange = (val) => {
                    queryParams.pageNum = val;
                    handleQuery();
                };
                
                return {
                    loading,
                    total,
                    queryParams,
                    crowdList,
                    handleQuery,
                    resetQuery,
                    handleAdd,
                    handleView,
                    handleEdit,
                    handleStats,
                    handleDelete,
                    handleExport,
                    handleImport,
                    handleColumnSetting,
                    handleSizeChange,
                    handleCurrentChange
                };
            }
        }).use(ElementPlus).mount('body');
    </script>
</body>
</html>
